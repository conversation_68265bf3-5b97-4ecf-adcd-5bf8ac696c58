document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');
    
    if (!token) {
        mostrarError('Token no válido. Por favor verifica el enlace.');
        return;
    }
    
    cargarDatosSeleccion(token);
});

let datosSeleccion = null;
let opcionSeleccionada = null;

async function cargarDatosSeleccion(token) {
    try {
        const url = `${window.API_URL_PHP}controller/get_datos_seleccion.php?token=${token}`;
        console.log('URL generada:', url);

        const response = await fetch(url);
        console.log('Response status:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Data recibida:', data);

        if (!data.exito) {
            mostrarError(data.error || 'Error al cargar los datos');
            return;
        }

        datosSeleccion = data.datos;
        mostrarOpcionesFecha();

    } catch (error) {
        console.error('Error completo:', error);
        mostrarError('Error de conexión: ' + error.message);
    }
}

function mostrarOpcionesFecha() {
    document.getElementById('loading').style.display = 'none';
    document.getElementById('seleccion-content').style.display = 'block';
    
    const container = document.getElementById('opciones-fechas');
    container.innerHTML = '';
    
    // Crear opciones de fecha
    const fechas = [
        { numero: 1, fecha: datosSeleccion.fecha_opcion_1 },
        { numero: 2, fecha: datosSeleccion.fecha_opcion_2 },
        { numero: 3, fecha: datosSeleccion.fecha_opcion_3 }
    ];
    
    fechas.forEach(opcion => {
        if (opcion.fecha) {
            const fechaFormateada = formatearFecha(opcion.fecha);
            const div = document.createElement('div');
            div.className = 'fecha-opcion';
            div.dataset.opcion = opcion.numero;
            div.innerHTML = `
                <div class="fecha-numero">Opción ${opcion.numero}</div>
                <div class="fecha-detalle">${fechaFormateada}</div>
            `;
            
            div.addEventListener('click', () => seleccionarOpcion(opcion.numero, div));
            container.appendChild(div);
        }
    });
    
    // Mostrar información adicional si existe
    mostrarInfoAdicional();
    
    // Configurar botón de confirmar
    document.getElementById('btn-confirmar').addEventListener('click', confirmarSeleccion);
}

function seleccionarOpcion(numero, elemento) {
    // Remover selección anterior
    document.querySelectorAll('.fecha-opcion').forEach(el => {
        el.classList.remove('seleccionada');
    });
    
    // Agregar selección actual
    elemento.classList.add('seleccionada');
    opcionSeleccionada = numero;
    
    // Habilitar botón de confirmar
    document.getElementById('btn-confirmar').disabled = false;
}

function mostrarInfoAdicional() {
    const infoContainer = document.getElementById('info-adicional');
    let tieneInfo = false;
    
    if (datosSeleccion.modo_contacto) {
        document.getElementById('modo-contacto').innerHTML = `<strong>Modo de contacto:</strong> ${datosSeleccion.modo_contacto}`;
        tieneInfo = true;
    }
    
    if (datosSeleccion.detalle_contacto) {
        document.getElementById('detalle-contacto').innerHTML = `<strong>Dirección:</strong> ${datosSeleccion.detalle_contacto}`;
        tieneInfo = true;
    }
    
    if (datosSeleccion.link_videollamada) {
        document.getElementById('link-videollamada').innerHTML = `<strong>Enlace de videollamada:</strong> <a href="${datosSeleccion.link_videollamada}" target="_blank">${datosSeleccion.link_videollamada}</a>`;
        tieneInfo = true;
    }
    
    if (datosSeleccion.tipo_apoyo) {
        document.getElementById('tipo-apoyo').innerHTML = `<strong>Tipo de apoyo:</strong> ${datosSeleccion.tipo_apoyo}`;
        tieneInfo = true;
    }
    
    if (tieneInfo) {
        infoContainer.style.display = 'block';
    }
}

async function confirmarSeleccion() {
    if (!opcionSeleccionada) {
        alert('Por favor selecciona una fecha');
        return;
    }
    
    const btnConfirmar = document.getElementById('btn-confirmar');
    btnConfirmar.disabled = true;
    btnConfirmar.textContent = 'Confirmando...';
    
    try {
        const urlParams = new URLSearchParams(window.location.search);
        const token = urlParams.get('token');
        
        const response = await fetch(`${window.API_URL_PHP}controller/procesar_seleccion.php`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                token: token,
                opcion_seleccionada: opcionSeleccionada
            })
        });
        
        const data = await response.json();
        
        if (data.exito) {
            mostrarExito();
        } else {
            mostrarError(data.error || 'Error al confirmar la selección');
            btnConfirmar.disabled = false;
            btnConfirmar.textContent = 'Confirmar Selección';
        }
        
    } catch (error) {
        console.error('Error:', error);
        mostrarError('Error de conexión. Por favor intenta de nuevo.');
        btnConfirmar.disabled = false;
        btnConfirmar.textContent = 'Confirmar Selección';
    }
}

function mostrarExito() {
    document.getElementById('seleccion-content').style.display = 'none';
    document.getElementById('exito-container').style.display = 'block';
    
    // Mostrar la fecha seleccionada
    const fechaSeleccionada = datosSeleccion[`fecha_opcion_${opcionSeleccionada}`];
    document.getElementById('fecha-confirmada').textContent = formatearFecha(fechaSeleccionada);
}

function mostrarError(mensaje) {
    document.getElementById('loading').style.display = 'none';
    document.getElementById('seleccion-content').style.display = 'none';
    document.getElementById('error-container').style.display = 'block';
    document.getElementById('error-message').textContent = mensaje;
}

function formatearFecha(fechaISO) {
    if (!fechaISO) return '';
    
    try {
        const fecha = new Date(fechaISO);
        const opciones = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        return fecha.toLocaleDateString('es-ES', opciones);
    } catch (error) {
        return fechaISO;
    }
}
