<?php
// responder_contacto.php - Controlador que redirige al HTML

// Cargar variables de entorno
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../..');
$dotenv->load();

$token = $_GET['token'] ?? null;

if ($token) {
    // URL base usando tu configuración existente
    $base_url = ($_ENV['ENVIRONMENT'] === 'localhost') ? $_ENV['DEV_BASE_URL'] : $_ENV['BASE_URL'];

    // Redirigir al HTML con el token como parámetro
    header("Location: " . $base_url . "/Frontend/views/responder_contacto.html?token=" . urlencode($token));
    exit;
} else {
    // Si no hay token, redirigir a la página de inicio
    header("Location: /index.html");
    exit;
}
?>
