<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title>Responder a Contacto</title>
    <link rel="stylesheet" href="../css/responder_contacto.css">
</head>
<body>
<div class="contenedor">
    <div id="error-container" style="display: none;">
        <h1>⚠️ Enlace inválido</h1>
        <p id="error-message" style="color: red; font-weight: bold;"></p>
        <p>Serás redirigido en 5 segundos a la página de inicio.</p>
    </div>

    <div id="contacto-container" style="display: none;">
        <h1>📩 Confirmar Disponibilidad</h1>

        <p class="dato" id="nombre-contacto"></p>
        <p><strong>Email:</strong> <span id="email-contacto"></span></p>
        <p><strong>Teléfono:</strong> <span id="telefono-contacto"></span></p>
        <p><strong>Descripción:</strong> <span id="descripcion-contacto"></span></p>
        <p><strong>Periodo sugerido:</strong> <span id="periodo-contacto"></span></p>

        <p style="margin-top: 2rem; font-size: 1.1rem;">
            Este podría ser el comienzo de una gran colaboración. Elige tres momentos que te acomoden para reunirte y selecciona la modalidad que más te haga sentido. ¡Queremos facilitar tu talento!
        </p>

        <form id="formulario-respuesta" method="post" action=""">
            <input type="hidden" name="token" id="token-hidden">

            <label>Fecha y hora sugerida 1:</label>
            <input type="datetime-local" name="fecha1" required>

            <label>Fecha y hora sugerida 2 (opcional):</label>
            <input type="datetime-local" name="fecha2">

            <label>Fecha y hora sugerida 3 (opcional):</label>
            <input type="datetime-local" name="fecha3">

            <label>¿Cómo prefieres juntarte?</label>
            <select name="modo_contacto" id="modo_contacto" required>
                <option value="">Selecciona una opción</option>
                <option value="presencial">Presencial</option>
                <option value="videollamada">Videollamada</option>
                <option value="agente">Con apoyo de un agente de Match Creativo</option>
            </select>

            <div id="grupo_direccion" class="extra">
                <label>Dirección física:</label>
                <input type="text" name="direccion" placeholder="Ej: Av. Providencia 123, Santiago">
            </div>

            <div id="grupo_link" class="extra">
                <label>Enlace de videollamada:</label>
                <input type="url" name="link_videollamada" placeholder="https://meet.google.com/...">
            </div>

            <div id="grupo_apoyo" class="extra">
                <label>¿Qué tipo de apoyo necesitas?</label>
                <select name="tipo_apoyo">
                    <option value="">Selecciona</option>
                    <option value="acompanamiento">Acompañamiento durante la conversación</option>
                    <option value="asesoria">Asesoría en propuesta creativa</option>
                    <option value="definicion_necesidades">Ayuda para definir mejor mis necesidades</option>
                </select>
            </div>

            <button type="submit">Enviar Respuesta</button>
        </form>
    </div>
</div>

<script src="../js/config.js"></script>
<script src="../js/responder_contacto.js"></script>
<script>

document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('formulario-respuesta');
    if (form) {
        form.action = window.API_URL_PHP + 'controller/post_guardar_contacto.php';
    }
});
</script>
</body>
</html>