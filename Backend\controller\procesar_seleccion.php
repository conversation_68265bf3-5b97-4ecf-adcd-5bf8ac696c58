<?php
// procesar_seleccion.php

header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

// Habilitar reporte de errores para debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Manejar preflight OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

require '../../vendor/autoload.php';

// Cargar variables de entorno
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../..');
$dotenv->load();

// Conexión a base de datos
require_once '../models/conexion.php';

// Obtener datos JSON
$input = file_get_contents('php://input');
$data = json_decode($input, true);

$token = $data['token'] ?? null;
$opcion_seleccionada = $data['opcion_seleccionada'] ?? null;

if (!$token || !$opcion_seleccionada) {
    echo json_encode(['exito' => false, 'error' => 'Datos incompletos']);
    exit;
}

if (!in_array($opcion_seleccionada, [1, 2, 3])) {
    echo json_encode(['exito' => false, 'error' => 'Opción inválida']);
    exit;
}

try {
    // Iniciar transacción
    $conn->beginTransaction();
    
    // Verificar token y obtener datos
    $stmt = $conn->prepare("
        SELECT 
            ts.id_contacto,
            ts.fecha_opcion_1,
            ts.fecha_opcion_2, 
            ts.fecha_opcion_3,
            ts.modo_contacto,
            ts.detalle_contacto,
            ts.link_videollamada,
            ts.tipo_apoyo,
            ts.usado_en,
            c.nombre,
            c.email,
            c.id_perfil_creativo
        FROM tokens_seleccion ts
        JOIN contactos c ON ts.id_contacto = c.id
        WHERE ts.token = ?
    ");
    
    $stmt->execute([$token]);
    $datos = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$datos) {
        throw new Exception('Token inválido o expirado');
    }
    
    if ($datos['usado_en']) {
        throw new Exception('Este enlace ya ha sido utilizado');
    }
    
    // Verificar si ya existe una selección
    $stmt_check = $conn->prepare("SELECT id FROM selecciones_cliente WHERE id_contacto = ?");
    $stmt_check->execute([$datos['id_contacto']]);
    
    if ($stmt_check->fetch()) {
        throw new Exception('Ya has seleccionado una fecha para este contacto');
    }
    
    // Obtener la fecha seleccionada
    $fecha_seleccionada = $datos["fecha_opcion_$opcion_seleccionada"];
    
    if (!$fecha_seleccionada) {
        throw new Exception('La opción seleccionada no tiene fecha válida');
    }
    
    // Insertar selección del cliente
    $stmt_insert = $conn->prepare("
        INSERT INTO selecciones_cliente 
        (id_contacto, fecha_seleccionada, opcion_seleccionada, modo_contacto, detalle_contacto, link_videollamada, tipo_apoyo)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ");
    
    $stmt_insert->execute([
        $datos['id_contacto'],
        $fecha_seleccionada,
        $opcion_seleccionada,
        $datos['modo_contacto'],
        $datos['detalle_contacto'],
        $datos['link_videollamada'],
        $datos['tipo_apoyo']
    ]);
    
    // Marcar token como usado
    $stmt_update = $conn->prepare("UPDATE tokens_seleccion SET usado_en = NOW() WHERE token = ?");
    $stmt_update->execute([$token]);
    
    // Actualizar estado del contacto a "Match Confirmado"
    $stmt_estado = $conn->prepare("UPDATE contactos SET estado = 'Match Confirmado' WHERE id = ?");
    $stmt_estado->execute([$datos['id_contacto']]);
    
    // Obtener datos del creativo para notificación
    $stmt_creativo = $conn->prepare("SELECT correo, nombre, apellido FROM perfil_creativo WHERE id_pcreativa = ?");
    $stmt_creativo->execute([$datos['id_perfil_creativo']]);
    $creativo = $stmt_creativo->fetch(PDO::FETCH_ASSOC);
    
    // Confirmar transacción
    $conn->commit();
    
    // Enviar notificación al creativo
    if ($creativo && $creativo['correo']) {
        enviarNotificacionCreativo($creativo, $datos, $fecha_seleccionada, $opcion_seleccionada);
    }
    
    echo json_encode([
        'exito' => true,
        'mensaje' => 'Selección confirmada exitosamente'
    ]);
    
} catch (Exception $e) {
    // Revertir transacción en caso de error
    $conn->rollBack();
    error_log("procesar_seleccion.php - Error: " . $e->getMessage());
    echo json_encode(['exito' => false, 'error' => $e->getMessage()]);
}

function enviarNotificacionCreativo($creativo, $datos_contacto, $fecha_seleccionada, $opcion) {
    try {
        $mail = new PHPMailer(true);
        
        $mail->isSMTP();
        $mail->Host       = $_ENV['MAIL_HOST'];
        $mail->SMTPAuth   = true;
        $mail->Username   = $_ENV['MAIL_USERNAME'];
        $mail->Password   = $_ENV['MAIL_PASSWORD'];
        $mail->SMTPSecure = 'tls';
        $mail->Port       = $_ENV['MAIL_PORT'];
        $mail->CharSet = 'UTF-8';

        $mail->setFrom($_ENV['MAIL_FROM'], $_ENV['MAIL_FROM_NAME']);
        $mail->addAddress($creativo['correo'], $creativo['nombre'] . ' ' . $creativo['apellido']);

        $fecha_formateada = (new DateTime($fecha_seleccionada))->format('d/m/Y H:i');
        
        $mail->isHTML(true);
        $mail->Subject = '¡Cliente confirmó fecha de reunión!';
        $mail->Body = '
            <div style="background:#181818;padding:32px 0;font-family:Arial,sans-serif;">
                <div style="background:#fff3e0;max-width:500px;margin:0 auto;border-radius:12px;box-shadow:0 4px 16px rgba(0,0,0,0.08);padding:32px 28px;">
                    <h2 style="color:#ff6600;text-align:center;margin-top:0;">🎉 ¡Cliente confirmó la reunión!</h2>
                    <hr style="border:0;border-top:2px solid #ff6600;margin:16px 0;">
                    <p style="color:#181818;font-size:16px;"><strong>Cliente:</strong> ' . htmlspecialchars($datos_contacto['nombre']) . '</p>
                    <p style="color:#181818;font-size:16px;"><strong>Email:</strong> ' . htmlspecialchars($datos_contacto['email']) . '</p>
                    <p style="color:#181818;font-size:16px;"><strong>Fecha seleccionada:</strong> <span style="color:#ff6600;font-weight:bold;">' . $fecha_formateada . '</span></p>
                    <p style="color:#181818;font-size:16px;"><strong>Opción elegida:</strong> Opción ' . $opcion . '</p>
                    ' . ($datos_contacto['modo_contacto'] ? '<p style="color:#181818;font-size:16px;"><strong>Modo de contacto:</strong> ' . htmlspecialchars($datos_contacto['modo_contacto']) . '</p>' : '') . '
                    ' . ($datos_contacto['detalle_contacto'] ? '<p style="color:#181818;font-size:16px;"><strong>Dirección:</strong> ' . htmlspecialchars($datos_contacto['detalle_contacto']) . '</p>' : '') . '
                    ' . ($datos_contacto['link_videollamada'] ? '<p style="color:#181818;font-size:16px;"><strong>Enlace:</strong> <a href="' . htmlspecialchars($datos_contacto['link_videollamada']) . '">' . htmlspecialchars($datos_contacto['link_videollamada']) . '</a></p>' : '') . '
                    <hr style="border:0;border-top:2px solid #ff6600;margin:24px 0 12px 0;">
                    <p style="color:#ff6600;text-align:center;font-size:18px;font-weight:bold;margin-bottom:0;">¡Es hora de brillar! 🌟</p>
                </div>
            </div>
        ';

        $mail->send();
        error_log("Notificación enviada al creativo: " . $creativo['correo']);
        
    } catch (Exception $e) {
        error_log("Error al enviar notificación al creativo: " . $e->getMessage());
    }
}
?>
