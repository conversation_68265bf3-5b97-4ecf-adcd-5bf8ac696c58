<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Perfil Creativo - Match Creativo</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../css/index.css">
    <link rel="stylesheet" href="../css/perfil.css">
    <link rel="stylesheet" href="../css/contacto_popup.css">
     <link rel="stylesheet" href="../css/contacto_popup2.css">
    
</head>
<body>
    <div class="volver">
        <a href="../views/lista_creativos.html">Volver</a>
    </div>

    <div class="perfil-container">
        <div class="perfil-header">
            <div class="perfil-foto">
                <img id="foto-perfil" src="../assets/default-profile.jpg" alt="Foto de perfil">
            </div>
            <h1 id="nombre-completo">Nombre Apellido <img id="icono-verificado" src="../../Assets/icon/verificado.png" alt="Verificado" class="icono-verificado" title="Perfil verificado"></h1>
            <p id="titulo-profesional">Título Profesional</p>
            <p><strong>Estado:</strong> <span id="estado-disponibilidad" class="estado-disponibilidad" style="color: #4CAF50; font-weight: bold;" title="Disponible">🟢 Disponible</span></p>
            <button id="btn-contactar" class="btn-contactar">Contactar</button>
        </div>

        <div class="perfil-seccion">
            <h2>Descripción</h2>
            <p id="perfil-descripcion">Cargando descripción...</p>
        </div>

        <div class="perfil-seccion">
            <h2>Proyectos</h2>
            <div class="proyectos-container">
                
            </div>
        </div>

        <div class="perfil-seccion">
            <h2>Experiencia</h2>
            <div class="experiencia-container">
                <div class="experiencia-item">
                    <div class="experiencia-periodo">
                        <span id="periodo-fecha">Desde <span id="startdate">--/--/----</span> hasta <span id="enddate">Presente</span></span>
                    </div>
                    <div class="experiencia-contenido">
                        <p id="experiencia">Cargando experiencia...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Popup de contacto -->
    <div id="contacto-popup" class="popup-container">
  <div class="popup-content">
    <span class="cerrar-popup">&times;</span>
    <h2>Contactar al creativo</h2>
   <form id="contacto-form">
  <div class="form-group">
    <label for="nombre">Nombre</label>
    <input type="text" id="nombre" name="nombre" required>
  </div>

  <div class="form-group">
    <label for="telefono">Número de Teléfono o WhatsApp</label>
    <input type="tel" id="telefono" name="telefono">
  </div>

  <div class="form-group">
    <label for="email">Correo electrónico</label>
    <input type="email" id="email" name="email">
  </div>

  <div class="form-group">
    <label for="descripcion">Descripción</label>
    <textarea id="descripcion" name="descripcion" rows="4"></textarea>
  </div>

  <!-- Mensaje UX antes de las fechas -->
  <div class="form-group aviso-fechas">
    📅 <strong>Importante:</strong> Elige un <u>rango de fechas</u> en el que puedas reunirte. El creativo decidirá el mejor momento para el primer match.
  </div>

  <div class="form-group">
    <label for="fechainicio">Fecha de inicio</label>
    <input type="date" id="fechainicio" name="fechainicio">
  </div>

  <div class="form-group">
    <label for="fechatermino">Fecha de término</label>
    <input type="date" id="fechatermino" name="fechatermino">
  </div>

  <input type="hidden" id="id_perfil_creativo" name="id_perfil_creativo" value="1">

  <!-- Área para mostrar mensaje (éxito / error) -->
  <div id="mensaje-contacto" class="mensaje-container" style="display:none;"></div>

  <div class="form-actions">
    <button type="submit" class="btn-enviar">Enviar</button>
  </div>
</form>

    <div id="mensaje-contacto" class="mensaje-container"></div>
  </div>
</div>

<!-- Popup de datos de contacto del creativo -->
<div id="popup-datos-contacto" class="popup-container">
  <div class="popup-content contacto-datos">
    <h3>Contacto</h3>
    <hr>
    <div id="datos-contacto-info">
      <!-- Se completa desde JS -->
    </div>
    <div style="text-align: center;">
      <button id="cerrar-datos-contacto" class="btn-enviar cerrar-btn">Cerrar</button>
    </div>
  </div>
</div>

    <script src="../js/config.js"></script>

    <script src="../js/perfil_creativo.js"></script>
    <script src="../js/contacto_popup.js"></script>
</body>
</html>
