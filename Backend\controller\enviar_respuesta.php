<?php
// enviar_respuesta.php

// Headers CORS y manejo de errores
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

// Habilitar reporte de errores para debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Manejar preflight OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

require '../../vendor/autoload.php';

// Cargar variables de entorno
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../..');
$dotenv->load();

// Conexión a base de datos
require_once '../models/conexion.php';

// Log de datos recibidos para debug
error_log("enviar_respuesta.php - Datos POST recibidos: " . print_r($_POST, true));

// Recibir datos del formulario o petición POST
$id_contacto      = $_POST['id_contacto'] ?? null;
$fecha_opcion_1   = $_POST['fecha_opcion_1'] ?? '';
$fecha_opcion_2   = $_POST['fecha_opcion_2'] ?? '';
$fecha_opcion_3   = $_POST['fecha_opcion_3'] ?? '';
$modo_contacto    = $_POST['modo_contacto'] ?? '';
$link_videollamada= $_POST['link_videollamada'] ?? '';
$tipo_apoyo       = $_POST['tipo_apoyo'] ?? '';
$detalle_contacto = $_POST['detalle_contacto'] ?? '';

// Validar datos requeridos
if (!$id_contacto) {
    error_log("enviar_respuesta.php - Error: Falta el id_contacto");
    echo json_encode(['exito' => false, 'error' => 'Falta el id_contacto']);
    exit;
}

try {
    // Obtener email y nombre del cliente desde la tabla contactos
    $stmt = $conn->prepare('SELECT email, nombre FROM contactos WHERE id = ?');
    $stmt->execute([$id_contacto]);
    $cliente = $stmt->fetch(PDO::FETCH_ASSOC);

    error_log("enviar_respuesta.php - Cliente encontrado: " . print_r($cliente, true));

    if (!$cliente) {
        error_log("enviar_respuesta.php - Error: No se encontró el cliente con ID: " . $id_contacto);
        echo json_encode(['exito' => false, 'error' => 'No se encontró el cliente']);
        exit;
    }

    if (!$cliente['email']) {
        error_log("enviar_respuesta.php - Error: El cliente no tiene email registrado. ID: " . $id_contacto);
        echo json_encode(['exito' => false, 'error' => 'El cliente no tiene email registrado']);
        exit;
    }

    // Validar que las variables de entorno estén configuradas
    $required_env_vars = ['MAIL_HOST', 'MAIL_USERNAME', 'MAIL_PASSWORD', 'MAIL_PORT', 'MAIL_FROM', 'MAIL_FROM_NAME'];
    foreach ($required_env_vars as $var) {
        if (!isset($_ENV[$var]) || empty($_ENV[$var])) {
            error_log("enviar_respuesta.php - Error: Variable de entorno faltante: " . $var);
            echo json_encode(['exito' => false, 'error' => 'Configuración de correo incompleta']);
            exit;
        }
    }
function formatearFecha($fechaISO) {
    if (empty($fechaISO)) return '';
    
    try {
        $fecha = new DateTime($fechaISO);
        return $fecha->format('d/m/Y H:i'); // Formato: 12/07/2025 18:33
        // O si prefieres: return $fecha->format('d/m/Y \a \l\a\s H:i');
    } catch (Exception $e) {
        return $fechaISO; // Si falla, devolver original
    }
}

// Formatear las fechas antes del correo
$fecha_opcion_1_formateada = formatearFecha($fecha_opcion_1);
$fecha_opcion_2_formateada = formatearFecha($fecha_opcion_2);
$fecha_opcion_3_formateada = formatearFecha($fecha_opcion_3); 
    // Preparar el correo
    $mail = new PHPMailer(true);
    
    $mail->isSMTP();
    $mail->Host       = $_ENV['MAIL_HOST'];
    $mail->SMTPAuth   = true;
    $mail->Username   = $_ENV['MAIL_USERNAME'];
    $mail->Password   = $_ENV['MAIL_PASSWORD'];
    $mail->SMTPSecure = 'tls';
    $mail->Port       = $_ENV['MAIL_PORT'];
    $mail->CharSet = 'UTF-8';

    // Configurar remitente y destinatario
    $mail->setFrom($_ENV['MAIL_FROM'], $_ENV['MAIL_FROM_NAME']);
    $mail->addAddress($cliente['email'], $cliente['nombre']);

    // Generar token único para la selección
    $token_seleccion = bin2hex(random_bytes(16));

    // Guardar token en base de datos
    $stmt_token = $conn->prepare("INSERT INTO tokens_seleccion (id_contacto, token, fecha_opcion_1, fecha_opcion_2, fecha_opcion_3, modo_contacto, detalle_contacto, link_videollamada, tipo_apoyo) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
    $stmt_token->execute([$id_contacto, $token_seleccion, $fecha_opcion_1, $fecha_opcion_2, $fecha_opcion_3, $modo_contacto, $detalle_contacto, $link_videollamada, $tipo_apoyo]);

    // URL base para la selección
    $base_url = isset($_ENV['BASE_URL']) ? $_ENV['BASE_URL'] : 'http://localhost/match-creativo';
    $url_seleccion = $base_url . '/Frontend/views/seleccionar_fecha.html?token=' . $token_seleccion;

    $mail->isHTML(true);
    $mail->Subject = 'Respuesta a tu solicitud de contacto';
    $mail->Body    = '
        <div style="background:#181818;padding:32px 0;font-family:Arial,sans-serif;">
            <div style="background:#fff3e0;max-width:500px;margin:0 auto;border-radius:12px;box-shadow:0 4px 16px rgba(0,0,0,0.08);padding:32px 28px;">
                <h2 style="color:#ff6600;text-align:center;margin-top:0;">¡El creativo ha respondido a tu solicitud!</h2>
                <hr style="border:0;border-top:2px solid #ff6600;margin:16px 0;">
                <p style="color:#181818;font-size:16px;margin-bottom:8px;"><strong>Fechas propuestas:</strong></p>
                <ul style="color:#181818;font-size:15px;padding-left:20px;margin-top:0;">
                    <li><strong>Opción 1:</strong> ' . htmlspecialchars($fecha_opcion_1_formateada) . '</li>
                    <li><strong>Opción 2:</strong> ' . htmlspecialchars($fecha_opcion_2_formateada) . '</li>
                    <li><strong>Opción 3:</strong> ' . htmlspecialchars($fecha_opcion_3_formateada) . '</li>
                </ul>
                <p style="color:#181818;font-size:16px;margin-bottom:4px;"><strong>Modo de contacto:</strong> <span style="color:#ff6600;">' . htmlspecialchars($modo_contacto) . '</span></p>
                '. ($link_videollamada ? '<p style="color:#181818;font-size:16px;margin-bottom:4px;"><strong>Enlace de videollamada:</strong> <a href="'.htmlspecialchars($link_videollamada).'" style="color:#ff6600;">'.htmlspecialchars($link_videollamada).'</a></p>' : '') . '
                '. ($tipo_apoyo ? '<p style="color:#181818;font-size:16px;margin-bottom:4px;"><strong>Tipo de apoyo:</strong> <span style="color:#ff6600;">'.htmlspecialchars($tipo_apoyo).'</span></p>' : '') . '
                '. ($detalle_contacto ? '<p style="color:#181818;font-size:16px;margin-bottom:4px;"><strong>Dirección:</strong> <span style="color:#ff6600;">'.htmlspecialchars($detalle_contacto).'</span></p>' : '') . '
                <hr style="border:0;border-top:2px solid #ff6600;margin:24px 0 16px 0;">
                <div style="text-align:center;margin-bottom:24px;">
                    <p style="color:#181818;font-size:16px;margin-bottom:16px;"><strong>¡Ahora elige tu fecha preferida!</strong></p>
                    <a href="' . $url_seleccion . '" style="display:inline-block;background:#ff6600;color:white;padding:12px 24px;text-decoration:none;border-radius:8px;font-weight:bold;font-size:16px;">📅 Seleccionar Fecha</a>
                </div>
                <hr style="border:0;border-top:2px solid #ff6600;margin:24px 0 12px 0;">
                <p style="color:#ff6600;text-align:center;font-size:18px;font-weight:bold;margin-bottom:0;">¡Gracias por usar Match Creativo!</p>
            </div>
        </div>
    ';

    error_log("enviar_respuesta.php - Intentando enviar correo a: " . $cliente['email']);
    
    $mail->send();
    
    error_log("enviar_respuesta.php - Correo enviado exitosamente a: " . $cliente['email']);
    echo json_encode(['exito' => true, 'mensaje' => 'Correo enviado al cliente']);
    
} catch (PDOException $e) {
    error_log("enviar_respuesta.php - Error de base de datos: " . $e->getMessage());
    echo json_encode(['exito' => false, 'error' => 'Error de base de datos: ' . $e->getMessage()]);
} catch (Exception $e) {
    error_log("enviar_respuesta.php - Error al enviar correo: " . $e->getMessage());
    echo json_encode(['exito' => false, 'error' => 'Error al enviar correo: ' . $e->getMessage()]);
} catch (Throwable $e) {
    error_log("enviar_respuesta.php - Error general: " . $e->getMessage());
    echo json_encode(['exito' => false, 'error' => 'Error general: ' . $e->getMessage()]);
} 