<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Seleccionar <PERSON> - Match Creativo</title>
    <link rel="stylesheet" href="../css/styles.css">
    <style>
        .seleccion-container {
            max-width: 600px;
            margin: 2rem auto;
            padding: 2rem;
            background: #fff3e0;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }
        
        .fecha-opcion {
            background: white;
            border: 2px solid #ff6600;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .fecha-opcion:hover {
            background: #ff6600;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 102, 0, 0.3);
        }
        
        .fecha-opcion.seleccionada {
            background: #ff6600;
            color: white;
        }
        
        .fecha-numero {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .fecha-detalle {
            font-size: 1.1rem;
        }
        
        .btn-confirmar {
            background: #ff6600;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            margin-top: 2rem;
            transition: background 0.3s ease;
        }
        
        .btn-confirmar:hover {
            background: #e55a00;
        }
        
        .btn-confirmar:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .info-adicional {
            background: #f8f9fa;
            border-left: 4px solid #ff6600;
            padding: 1rem;
            margin: 1.5rem 0;
            border-radius: 4px;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
        }
        
        .error {
            background: #ffe6e6;
            border: 1px solid #ff6b6b;
            color: #d63031;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        
        .exito {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="seleccion-container">
            <div id="loading" class="loading">
                <h2>Cargando información...</h2>
                <p>Por favor espera mientras verificamos tu solicitud.</p>
            </div>
            
            <div id="error-container" style="display: none;">
                <h2>❌ Error</h2>
                <div id="error-message" class="error"></div>
            </div>
            
            <div id="seleccion-content" style="display: none;">
                <h1 style="text-align: center; color: #ff6600;">📅 Selecciona tu fecha preferida</h1>
                <p style="text-align: center; margin-bottom: 2rem;">El creativo ha propuesto las siguientes fechas. Elige la que mejor se adapte a tu horario:</p>
                
                <div id="opciones-fechas"></div>
                
                <div id="info-adicional" class="info-adicional" style="display: none;">
                    <h3>Información adicional:</h3>
                    <div id="modo-contacto"></div>
                    <div id="detalle-contacto"></div>
                    <div id="link-videollamada"></div>
                    <div id="tipo-apoyo"></div>
                </div>
                
                <button id="btn-confirmar" class="btn-confirmar" disabled>
                    Confirmar Selección
                </button>
            </div>
            
            <div id="exito-container" style="display: none;">
                <div class="exito">
                    <h2>✅ ¡Fecha confirmada!</h2>
                    <p>Tu selección ha sido enviada al creativo. Te contactará pronto para coordinar los detalles.</p>
                    <p><strong>Fecha seleccionada:</strong> <span id="fecha-confirmada"></span></p>
                </div>
            </div>
        </div>
    </div>
    
    <script src="../js/config.js"></script>
    <script src="../js/seleccionar_fecha.js"></script>
</body>
</html>
