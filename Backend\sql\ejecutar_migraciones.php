<?php
// ejecutar_migraciones.php
// Script para crear las nuevas tablas necesarias para la selección de fechas

require_once '../models/conexion.php';

try {
    echo "Iniciando creación de tablas...\n";
    
    // Leer el archivo SQL
    $sql_content = file_get_contents(__DIR__ . '/crear_tablas_seleccion.sql');
    
    if (!$sql_content) {
        throw new Exception("No se pudo leer el archivo SQL");
    }
    
    // Dividir las consultas por punto y coma
    $queries = array_filter(array_map('trim', explode(';', $sql_content)));
    
    foreach ($queries as $query) {
        if (!empty($query)) {
            echo "Ejecutando: " . substr($query, 0, 50) . "...\n";
            $conn->exec($query);
            echo "✓ Completado\n";
        }
    }
    
    echo "\n¡Todas las tablas se crearon exitosamente!\n";
    echo "Tablas creadas:\n";
    echo "- tokens_seleccion\n";
    echo "- selecciones_cliente\n";
    echo "- Índices correspondientes\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
