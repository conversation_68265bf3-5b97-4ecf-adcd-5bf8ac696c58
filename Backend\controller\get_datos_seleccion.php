<?php
// get_datos_seleccion.php

header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

// Habilitar reporte de errores para debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Log para debug
error_log("get_datos_seleccion.php - Iniciando script");

// Manejar preflight OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Conexión a base de datos
require_once '../models/conexion.php';

// Obtener token de la URL
$token = $_GET['token'] ?? null;

error_log("get_datos_seleccion.php - Token recibido: " . ($token ? 'SÍ' : 'NO'));

if (!$token) {
    error_log("get_datos_seleccion.php - Error: Token no proporcionado");
    echo json_encode(['exito' => false, 'error' => 'Token requerido']);
    exit;
}

try {
    // Verificar token y obtener datos
    $stmt = $conn->prepare("
        SELECT 
            ts.id_contacto,
            ts.fecha_opcion_1,
            ts.fecha_opcion_2, 
            ts.fecha_opcion_3,
            ts.modo_contacto,
            ts.detalle_contacto,
            ts.link_videollamada,
            ts.tipo_apoyo,
            ts.usado_en,
            c.nombre,
            c.email
        FROM tokens_seleccion ts
        JOIN contactos c ON ts.id_contacto = c.id
        WHERE ts.token = ?
    ");
    
    $stmt->execute([$token]);
    $datos = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$datos) {
        echo json_encode(['exito' => false, 'error' => 'Token inválido o expirado']);
        exit;
    }
    
    if ($datos['usado_en']) {
        echo json_encode(['exito' => false, 'error' => 'Este enlace ya ha sido utilizado']);
        exit;
    }
    
    // Verificar si ya existe una selección para este contacto
    $stmt_check = $conn->prepare("SELECT id FROM selecciones_cliente WHERE id_contacto = ?");
    $stmt_check->execute([$datos['id_contacto']]);
    
    if ($stmt_check->fetch()) {
        echo json_encode(['exito' => false, 'error' => 'Ya has seleccionado una fecha para este contacto']);
        exit;
    }
    
    // Remover datos sensibles antes de enviar
    unset($datos['usado_en']);
    
    echo json_encode([
        'exito' => true,
        'datos' => $datos
    ]);
    
} catch (PDOException $e) {
    error_log("get_datos_seleccion.php - Error de base de datos: " . $e->getMessage());

    // Verificar si es error de tabla no existe
    if (strpos($e->getMessage(), "doesn't exist") !== false) {
        echo json_encode(['exito' => false, 'error' => 'Las tablas de selección no están creadas. Ejecuta las migraciones SQL primero.']);
    } else {
        echo json_encode(['exito' => false, 'error' => 'Error de base de datos: ' . $e->getMessage()]);
    }
} catch (Exception $e) {
    error_log("get_datos_seleccion.php - Error general: " . $e->getMessage());
    echo json_encode(['exito' => false, 'error' => 'Error del servidor: ' . $e->getMessage()]);
}
?>
