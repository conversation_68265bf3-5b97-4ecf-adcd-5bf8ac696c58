-- Tabla para almacenar tokens de selección de fecha
CREATE TABLE IF NOT EXISTS tokens_seleccion (
    id INT AUTO_INCREMENT PRIMARY KEY,
    id_contacto INT NOT NULL,
    token VARCHAR(32) NOT NULL UNIQUE,
    fecha_opcion_1 DATETIME,
    fecha_opcion_2 DATETIME,
    fecha_opcion_3 DATETIME,
    modo_contacto VARCHAR(100),
    detalle_contacto TEXT,
    link_videollamada VARCHAR(255),
    tipo_apoyo VARCHAR(100),
    usado_en DATETIME NULL,
    creado_en DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id_contacto) REFERENCES contactos(id) ON DELETE CASCADE
);

-- Tabla para almacenar las selecciones de fecha de los clientes
CREATE TABLE IF NOT EXISTS selecciones_cliente (
    id INT AUTO_INCREMENT PRIMARY KEY,
    id_contacto INT NOT NULL,
    fecha_seleccionada DATETIME NOT NULL,
    opcion_seleccionada TINYINT NOT NULL, -- 1, 2 o 3
    modo_contacto VARCHAR(100),
    detalle_contacto TEXT,
    link_videollamada VARCHAR(255),
    tipo_apoyo VARCHAR(100),
    creado_en DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id_contacto) REFERENCES contactos(id) ON DELETE CASCADE
);

-- Índices para mejorar rendimiento
CREATE INDEX idx_tokens_seleccion_token ON tokens_seleccion(token);
CREATE INDEX idx_tokens_seleccion_contacto ON tokens_seleccion(id_contacto);
CREATE INDEX idx_selecciones_cliente_contacto ON selecciones_cliente(id_contacto);
