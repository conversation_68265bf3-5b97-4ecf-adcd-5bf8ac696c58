document.addEventListener('DOMContentLoaded', function() {
    
    const urlParams = new URLSearchParams(window.location.search);
    const perfilId = urlParams.get('id');
    
    if (perfilId) {
        cargarPerfilCreativo(perfilId);
    } else {
        mostrarError('No se especificó un ID de perfil válido');
    }
    
  
});

async function cargarPerfilCreativo(id) {
    try {
        const response = await fetch(`${window.API_URL_PHP}controller/get_perfil_creativo.php?id=${id}`);
        const data = await response.json();
        
        if (data.exito) {
            mostrarDatosPerfil(data.perfil);
        } else {
            mostrarError('Error al cargar el perfil: ' + data.error);
        }
    } catch (error) {
        mostrarError('Error de conexión al cargar el perfil');
        console.error('Error:', error);
    }
}

function mostrarDatosPerfil(perfil) {
    // Función para obtener la ruta de assets según el entorno
    function getAssetPath(relativePath) {
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            return relativePath; // En local usa ruta relativa
        } else {
            return window.API_URL + relativePath.replace('../../', ''); // En servidor usa URL absoluta
        }
    }

    // Datos básicos
    const verificadoIcon = getAssetPath('../../Assets/icon/verificado.png');
    document.getElementById('nombre-completo').innerHTML = `${perfil.nombre} ${perfil.apellido} <img src="${verificadoIcon}" alt="Verificado" class="icono-verificado" title="Perfil verificado">`;
    document.getElementById('titulo-profesional').textContent = perfil.titulo_profesional;
    document.getElementById('perfil-descripcion').textContent = perfil.descripcion;
    document.getElementById('experiencia').textContent = perfil.experiencia;

    // Estado de disponibilidad
const estadoElement = document.getElementById('estado-disponibilidad');

if (perfil.estado_perfil == 1) {
  // Usar emoji en lugar de imagen para evitar problemas de ruta
  estadoElement.outerHTML = '<span id="estado-disponibilidad" class="estado-disponibilidad" style="color: #4CAF50; font-weight: bold;" title="Disponible">🟢 Disponible</span>';
} else {
  estadoElement.outerHTML = '<span id="estado-disponibilidad" class="estado-disponibilidad" style="color: #f44336; font-weight: bold;" title="No disponible">🔴 No disponible</span>';
}

estadoElement.setAttribute(
  'data-status',
  perfil.estado_perfil == 1 ? 'true' : 'false'
);

    // Fechas de experiencia
    const startDate = new Date(perfil.startdate);
    document.getElementById('startdate').textContent = formatearFecha(startDate);
    
    if (perfil.enddate) {
        const endDate = new Date(perfil.enddate);
        document.getElementById('enddate').textContent = formatearFecha(endDate);
    } else {
        document.getElementById('enddate').textContent = 'Presente';
    }
    
    // Foto de perfil
    if (perfil.foto_perfil) {
        document.getElementById('foto-perfil').src = perfil.foto_perfil;
    }
    
    // Cargar proyectos del creativo
    if (perfil.id_pcreativa) {
        cargarProyectos(perfil.id_pcreativa);
    }
}

function formatearFecha(fecha) {
    return fecha.toLocaleDateString('es-ES', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    });
}

function mostrarError(mensaje) {
    const perfilContainer = document.querySelector('.perfil-container');
    perfilContainer.innerHTML = `
        <div class="error-mensaje">
            <h2>Error</h2>
            <p>${mensaje}</p>
            <a href="../../index.html" class="btn-volver">Volver al inicio</a>
        </div>
    `;
}

async function cargarProyectos(id_pcreativa) {
    try {
        const response = await fetch(`${window.API_URL_PHP}controller/get_proyectos_creativo.php?id_pcreativa=${id_pcreativa}`);
        const data = await response.json();
        if (data.exito) {
            mostrarProyectos(data.proyectos);
        } else {
            mostrarProyectos([]);
        }
    } catch (error) {
        mostrarProyectos([]);
    }
}

function mostrarProyectos(proyectos) {
    const contenedor = document.querySelector('.proyectos-container');
    if (!proyectos.length) {
        contenedor.innerHTML = '<p>No hay proyectos disponibles</p>';
        return;
    }
    contenedor.innerHTML = '';
    proyectos.forEach(proy => {
        let imgSrc = proy.imagen_base64
            ? `data:image/jpeg;base64,${proy.imagen_base64}`
            : '../assets/default-project.jpg';
        const card = document.createElement('div');
        card.className = 'proyecto-card';
        card.innerHTML = `
            <img src="${imgSrc}" alt="Imagen del proyecto" class="proyecto-img">
            <h3>${proy.titulo_proyecto}</h3>
            
        `;
        contenedor.appendChild(card);
    });
}